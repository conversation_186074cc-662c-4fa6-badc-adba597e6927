#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 单一Prompt NER系统 - 真正的超级Prompt架构
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

from config import CONFIG, get_current_dataset_info
from model_interface import model_service

logger = logging.getLogger(__name__)


# 🚀 暂时移除自定义工具，直接使用现有的schemas中的工具
# 因为model_interface期望标准的Pydantic模型


class SinglePromptNER:
    """单一Prompt NER系统 - 核心引擎"""
    
    def __init__(self, vector_retriever=None):
        self.vector_retriever = vector_retriever
        self.model_service = model_service
        
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _generate_super_prompt(self, text: str) -> str:
        """生成单一超级Prompt - 包含完整的NER逻辑"""
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        prompt = f"""You are an expert Named Entity Recognition system with access to example retrieval tools.

Your task: Extract named entities from the given text using these entity types: {entity_types_str}

Available tools:
1. search_examples: Search for relevant NER examples from vector database (use when text is complex or ambiguous)
2. extract_entities: Extract entities directly from text (always call this as final step)

Process:
1. Analyze the input text complexity and entity patterns
2. If the text contains complex entities, abbreviations, or ambiguous references, call search_examples first
3. Always call extract_entities to provide the final result

Input text to analyze: "{text}"

Guidelines:
- For simple texts with clear entities, extract directly
- For complex texts with domain-specific terms, search for examples first
- Always provide reasoning for your extraction decisions
- Return entities in the exact format: {{"entity_type": ["entity1", "entity2"]}}
"""
        return prompt
    
    async def _handle_vector_search(self, query: str, max_examples: int = 3) -> List[Dict[str, Any]]:
        """处理向量检索请求"""
        try:
            if not self.vector_retriever or not self.vector_retriever.initialized:
                logger.warning("向量检索器未初始化，返回空结果")
                return []
            
            # 使用预初始化的向量库进行检索
            results = await self.vector_retriever.execute_retrieval_with_super_prompt(query)
            
            # 限制返回数量
            limited_results = results[:max_examples] if results else []
            
            logger.info(f"向量检索完成: 返回 {len(limited_results)} 个示例")
            return limited_results
            
        except Exception as e:
            logger.error(f"向量检索失败: {e}")
            return []
    
    def _format_examples_for_context(self, examples: List[Dict[str, Any]]) -> str:
        """将检索到的示例格式化为上下文"""
        if not examples:
            return "No examples found."
        
        formatted_examples = []
        for i, example in enumerate(examples, 1):
            text = example.get('text', '')
            labels = example.get('label', {})
            
            entities_str = ", ".join(
                f"'{entity}' ({etype})" 
                for etype, entities in labels.items() 
                for entity in entities
            )
            
            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")
        
        return "\n\n".join(formatted_examples)
    
    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """🚀 LLM自主决策的NER任务 - 简化但保持核心理念"""
        try:
            logger.info(f"🚀 开始单一Prompt NER: '{text[:50]}...'")

            # 🎯 LLM自主决策：基于文本复杂度智能选择策略
            final_prompt = await self._prepare_intelligent_prompt(text)

            # 使用ExtractionTool进行NER
            from schemas import ExtractionTool
            tools = [ExtractionTool]

            # 单次调用LLM
            messages = [{"role": "user", "content": final_prompt}]
            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            # 解析结果
            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                tool_call = response.tool_calls[0]
                if tool_call.function and tool_call.function.arguments:
                    try:
                        arguments = json.loads(tool_call.function.arguments)
                        entities = arguments.get("entities", {})

                        logger.info(f"✅ LLM完成实体提取: 识别 {sum(len(v) for v in entities.values())} 个实体")
                        return entities

                    except json.JSONDecodeError as e:
                        logger.error(f"解析工具参数失败: {e}")
                        return {}

            logger.warning("LLM未调用工具或返回无效结果")
            return {}

        except Exception as e:
            logger.error(f"单一Prompt NER失败: {e}")
            return {}

    async def _prepare_intelligent_prompt(self, text: str) -> str:
        """🧠 LLM自主决策：智能准备最优Prompt"""
        try:
            # 🎯 让LLM自主决策是否需要示例
            if self.vector_retriever and self.vector_retriever.initialized:
                logger.info("🧠 LLM自主决策：分析是否需要示例辅助...")

                # 让LLM决定是否需要示例
                needs_examples = await self._llm_decide_needs_examples(text)

                if needs_examples:
                    logger.info("🔍 LLM决定需要示例，正在检索...")
                    examples = await self._handle_vector_search(text, 3)
                    if examples:
                        examples_context = self._format_examples_for_context(examples)
                        return self._generate_enhanced_prompt(text, examples_context)
                else:
                    logger.info("🎯 LLM决定直接进行NER")

            # 无向量库或LLM决定不需要示例，使用基础Prompt
            return self._generate_super_prompt(text)

        except Exception as e:
            logger.warning(f"LLM决策失败: {e}，使用基础Prompt")
            return self._generate_super_prompt(text)

    async def _llm_decide_needs_examples(self, text: str) -> bool:
        """🧠 LLM自主决策：是否需要示例辅助"""
        try:
            entity_types = self._get_current_entity_types()
            entity_types_str = ', '.join(entity_types)

            decision_prompt = f"""You are an expert NER analyst. Analyze this text and decide if you need examples to help with entity extraction.

Text to analyze: "{text}"
Entity types to extract: {entity_types_str}

Consider these factors:
- Are there ambiguous entity boundaries?
- Are there domain-specific terms or abbreviations?
- Are there complex entity relationships?
- Are there potential nested entities?

Respond with ONLY "YES" if you need examples, or "NO" if you can extract entities directly.
"""

            messages = [{"role": "user", "content": decision_prompt}]
            response = await self.model_service.generate_simple_async(messages=messages)

            if response:
                decision = response.strip().upper()
                needs_examples = decision == "YES"
                logger.info(f"🧠 LLM决策结果: {'需要示例' if needs_examples else '直接NER'}")
                return needs_examples

            # 默认不需要示例
            logger.warning("LLM决策响应无效，默认直接NER")
            return False

        except Exception as e:
            logger.error(f"LLM决策失败: {e}，默认直接NER")
            return False

    def _generate_enhanced_prompt(self, text: str, examples_context: str) -> str:
        """生成包含示例的增强Prompt"""
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        return f"""You are an expert Named Entity Recognition system.

Learn from these relevant examples:
{examples_context}

Now extract named entities from this text using these entity types: {entity_types_str}

Text to analyze: "{text}"

Extract all named entities and return them using the extract_entities tool."""



# 全局实例
single_prompt_ner = None

def get_single_prompt_ner(vector_retriever=None):
    """获取单一Prompt NER实例"""
    global single_prompt_ner
    if single_prompt_ner is None:
        single_prompt_ner = SinglePromptNER(vector_retriever)
    return single_prompt_ner
