#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 单一Prompt NER系统 - 真正的超级Prompt架构
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

from config import CONFIG, get_current_dataset_info
from model_interface import model_service

logger = logging.getLogger(__name__)


# 🚀 暂时移除自定义工具，直接使用现有的schemas中的工具
# 因为model_interface期望标准的Pydantic模型


class SinglePromptNER:
    """单一Prompt NER系统 - 核心引擎"""
    
    def __init__(self, vector_retriever=None):
        self.vector_retriever = vector_retriever
        self.model_service = model_service
        
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _generate_super_prompt(self, text: str) -> str:
        """生成单一超级Prompt - 包含完整的NER逻辑"""
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        prompt = f"""You are an expert Named Entity Recognition system with access to example retrieval tools.

Your task: Extract named entities from the given text using these entity types: {entity_types_str}

Available tools:
1. search_examples: Search for relevant NER examples from vector database (use when text is complex or ambiguous)
2. extract_entities: Extract entities directly from text (always call this as final step)

Process:
1. Analyze the input text complexity and entity patterns
2. If the text contains complex entities, abbreviations, or ambiguous references, call search_examples first
3. Always call extract_entities to provide the final result

Input text to analyze: "{text}"

Guidelines:
- For simple texts with clear entities, extract directly
- For complex texts with domain-specific terms, search for examples first
- Always provide reasoning for your extraction decisions
- Return entities in the exact format: {{"entity_type": ["entity1", "entity2"]}}
"""
        return prompt
    
    async def _handle_vector_search(self, query: str, max_examples: int = 3) -> List[Dict[str, Any]]:
        """处理向量检索请求"""
        try:
            if not self.vector_retriever or not self.vector_retriever.initialized:
                logger.warning("向量检索器未初始化，返回空结果")
                return []
            
            # 使用预初始化的向量库进行检索
            results = await self.vector_retriever.execute_retrieval_with_super_prompt(query)
            
            # 限制返回数量
            limited_results = results[:max_examples] if results else []
            
            logger.info(f"向量检索完成: 返回 {len(limited_results)} 个示例")
            return limited_results
            
        except Exception as e:
            logger.error(f"向量检索失败: {e}")
            return []
    
    def _format_examples_for_context(self, examples: List[Dict[str, Any]]) -> str:
        """将检索到的示例格式化为上下文"""
        if not examples:
            return "No examples found."
        
        formatted_examples = []
        for i, example in enumerate(examples, 1):
            text = example.get('text', '')
            labels = example.get('label', {})
            
            entities_str = ", ".join(
                f"'{entity}' ({etype})" 
                for etype, entities in labels.items() 
                for entity in entities
            )
            
            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")
        
        return "\n\n".join(formatted_examples)
    
    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """🚀 LLM自主分析选择示例 → 筛选 → 拼接prompt → NER"""
        try:
            logger.info(f"🚀 开始LLM自主分析NER: '{text[:50]}...'")

            # 🧠 步骤1：LLM自主分析，决定需要什么样的示例
            example_requirements = await self._llm_analyze_example_needs(text)

            # 🔍 步骤2：基于LLM分析结果，检索相关示例
            selected_examples = await self._retrieve_examples_by_requirements(text, example_requirements)

            # 🎯 步骤3：LLM筛选最相关的示例
            filtered_examples = await self._llm_filter_examples(text, selected_examples)

            # 📝 步骤4：拼接最终prompt并进行NER
            final_prompt = self._build_final_ner_prompt(text, filtered_examples)

            # 🚀 步骤5：执行NER
            entities = await self._execute_ner_with_prompt(final_prompt)

            logger.info(f"✅ LLM自主分析NER完成: 识别 {sum(len(v) for v in entities.values())} 个实体")
            return entities

        except Exception as e:
            logger.error(f"LLM自主分析NER失败: {e}")
            return {}

# 🚀 移除旧的智能Prompt准备方法，使用新的LLM自主分析流程

    async def _llm_analyze_example_needs(self, text: str) -> Dict[str, Any]:
        """🧠 步骤1：LLM自主分析需要什么样的示例"""
        try:
            entity_types = self._get_current_entity_types()
            entity_types_str = ', '.join(entity_types)

            analysis_prompt = f"""You are an expert NER analyst. Analyze this text and determine what kind of examples would help with entity extraction.

Text to analyze: "{text}"
Entity types to extract: {entity_types_str}

Please analyze:
1. What specific challenges exist in this text for NER?
2. What types of examples would be most helpful?
3. What entity patterns should the examples demonstrate?
4. What domain or context should the examples come from?

Respond in JSON format:
{{
    "needs_examples": true/false,
    "challenges": ["challenge1", "challenge2"],
    "required_entity_types": ["type1", "type2"],
    "domain_context": "domain description",
    "example_patterns": ["pattern1", "pattern2"],
    "reasoning": "explanation of analysis"
}}
"""

            messages = [{"role": "user", "content": analysis_prompt}]
            response = await self.model_service.generate_simple_async(messages=messages)

            if response:
                try:
                    # 尝试解析JSON响应
                    analysis = json.loads(response.strip())
                    logger.info(f"🧠 LLM分析完成: {analysis.get('reasoning', 'N/A')[:50]}...")
                    return analysis
                except json.JSONDecodeError:
                    logger.warning("LLM分析响应不是有效JSON，使用默认策略")

            # 默认策略：不需要示例
            return {"needs_examples": False, "reasoning": "分析失败，使用直接NER"}

        except Exception as e:
            logger.error(f"LLM分析失败: {e}")
            return {"needs_examples": False, "reasoning": "分析异常，使用直接NER"}

    async def _llm_decide_needs_examples(self, text: str) -> bool:
        """🧠 LLM自主决策：是否需要示例辅助"""
        try:
            entity_types = self._get_current_entity_types()
            entity_types_str = ', '.join(entity_types)

            decision_prompt = f"""You are an expert NER analyst. Analyze this text and decide if you need examples to help with entity extraction.

Text to analyze: "{text}"
Entity types to extract: {entity_types_str}

Consider these factors:
- Are there ambiguous entity boundaries?
- Are there domain-specific terms or abbreviations?
- Are there complex entity relationships?
- Are there potential nested entities?

Respond with ONLY "YES" if you need examples, or "NO" if you can extract entities directly.
"""

            messages = [{"role": "user", "content": decision_prompt}]
            response = await self.model_service.generate_simple_async(messages=messages)

            if response:
                decision = response.strip().upper()
                needs_examples = decision == "YES"
                logger.info(f"🧠 LLM决策结果: {'需要示例' if needs_examples else '直接NER'}")
                return needs_examples

            # 默认不需要示例
            logger.warning("LLM决策响应无效，默认直接NER")
            return False

        except Exception as e:
            logger.error(f"LLM决策失败: {e}，默认直接NER")
            return False

    async def _retrieve_examples_by_requirements(self, text: str, requirements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """🔍 步骤2：基于LLM分析结果检索相关示例"""
        try:
            if not requirements.get("needs_examples", False):
                logger.info("🎯 LLM分析：不需要示例，直接进行NER")
                return []

            if not self.vector_retriever or not self.vector_retriever.initialized:
                logger.warning("⚠️ 向量库未初始化，无法检索示例")
                return []

            # 构建检索查询
            domain_context = requirements.get("domain_context", "")
            required_types = requirements.get("required_entity_types", [])

            # 使用原文本 + 领域上下文作为查询
            search_query = f"{text} {domain_context}".strip()

            logger.info(f"🔍 基于LLM分析检索示例: 领域={domain_context}, 实体类型={required_types}")

            # 🚀 使用原有的检索方法（保持LLM自主选择的核心逻辑）
            examples = await self._handle_vector_search(search_query, max_examples=10)

            logger.info(f"✅ 检索到 {len(examples)} 个候选示例")
            return examples

        except Exception as e:
            logger.error(f"示例检索失败: {e}")
            return []

    def _generate_enhanced_prompt(self, text: str, examples_context: str) -> str:
        """生成包含示例的增强Prompt"""
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        return f"""You are an expert Named Entity Recognition system.

Learn from these relevant examples:
{examples_context}

Now extract named entities from this text using these entity types: {entity_types_str}

Text to analyze: "{text}"

Extract all named entities and return them using the extract_entities tool."""

    async def _llm_filter_examples(self, text: str, candidate_examples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """🎯 步骤3：LLM筛选最相关的示例"""
        try:
            if not candidate_examples:
                logger.info("🎯 无候选示例，直接进行NER")
                return []

            # 格式化候选示例
            examples_text = ""
            for i, example in enumerate(candidate_examples):
                example_text = example.get('text', '')
                example_labels = example.get('label', {})
                entities_str = ", ".join(
                    f"'{entity}' ({etype})"
                    for etype, entities in example_labels.items()
                    for entity in entities
                )
                examples_text += f"Example {i+1}:\nText: {example_text}\nEntities: [{entities_str}]\n\n"

            filter_prompt = f"""You are an expert NER example selector. Given the target text and candidate examples, select the 3 most relevant examples that would help with entity extraction.

Target text: "{text}"

Candidate examples:
{examples_text}

Select the 3 most relevant examples by their numbers (e.g., "1,3,7"). Consider:
- Similarity in entity types and patterns
- Similarity in text structure and domain
- Usefulness for demonstrating entity boundaries

Respond with ONLY the numbers separated by commas (e.g., "1,3,7"):
"""

            messages = [{"role": "user", "content": filter_prompt}]
            response = await self.model_service.generate_simple_async(messages=messages)

            if response:
                try:
                    # 解析选择的示例编号
                    selected_indices = [int(x.strip()) - 1 for x in response.strip().split(',')]
                    selected_examples = [candidate_examples[i] for i in selected_indices if 0 <= i < len(candidate_examples)]

                    logger.info(f"🎯 LLM筛选完成: 从 {len(candidate_examples)} 个候选中选择了 {len(selected_examples)} 个示例")
                    return selected_examples[:3]  # 最多3个示例

                except (ValueError, IndexError) as e:
                    logger.warning(f"LLM筛选响应解析失败: {e}，使用前3个示例")
                    return candidate_examples[:3]

            # 默认返回前3个示例
            return candidate_examples[:3]

        except Exception as e:
            logger.error(f"示例筛选失败: {e}")
            return candidate_examples[:3] if candidate_examples else []

    def _build_final_ner_prompt(self, text: str, filtered_examples: List[Dict[str, Any]]) -> str:
        """📝 步骤4：拼接最终的NER prompt"""
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        if filtered_examples:
            # 有示例的情况
            examples_context = self._format_examples_for_context(filtered_examples)
            return f"""You are an expert Named Entity Recognition system.

Learn from these carefully selected examples:
{examples_context}

Now extract named entities from this text using these entity types: {entity_types_str}

Text to analyze: "{text}"

Extract all named entities and return them using the extract_entities tool."""
        else:
            # 无示例的情况
            return f"""You are an expert Named Entity Recognition system.

Extract named entities from the following text using these entity types: {entity_types_str}

Text to analyze: "{text}"

Extract all named entities and return them using the extract_entities tool."""

    async def _execute_ner_with_prompt(self, final_prompt: str) -> Dict[str, List[str]]:
        """🚀 步骤5：使用最终prompt执行NER"""
        try:
            from schemas import ExtractionTool
            tools = [ExtractionTool]

            messages = [{"role": "user", "content": final_prompt}]
            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            # 解析结果
            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                tool_call = response.tool_calls[0]
                if tool_call.function and tool_call.function.arguments:
                    try:
                        arguments = json.loads(tool_call.function.arguments)
                        entities = arguments.get("entities", {})
                        return entities

                    except json.JSONDecodeError as e:
                        logger.error(f"解析工具参数失败: {e}")
                        return {}

            logger.warning("LLM未调用工具或返回无效结果")
            return {}

        except Exception as e:
            logger.error(f"NER执行失败: {e}")
            return {}



# 全局实例
single_prompt_ner = None

def get_single_prompt_ner(vector_retriever=None):
    """获取单一Prompt NER实例"""
    global single_prompt_ner
    if single_prompt_ner is None:
        single_prompt_ner = SinglePromptNER(vector_retriever)
    return single_prompt_ner
