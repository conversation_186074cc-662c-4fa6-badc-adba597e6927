#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 单一Prompt NER系统 - 真正的超级Prompt架构
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

from config import CONFIG, get_current_dataset_info
from model_interface import model_service

logger = logging.getLogger(__name__)


# 🚀 简化架构：直接使用现有的ExtractionTool，移除复杂的多工具设计


class SinglePromptNER:
    """单一Prompt NER系统 - 核心引擎"""
    
    def __init__(self, vector_retriever=None):
        self.vector_retriever = vector_retriever
        self.model_service = model_service
        
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
# 🚀 移除复杂的超级Prompt，使用简化的直接Prompt策略
    
    async def _handle_vector_search(self, query: str, max_examples: int = 3) -> List[Dict[str, Any]]:
        """处理向量检索请求"""
        try:
            if not self.vector_retriever or not self.vector_retriever.initialized:
                logger.warning("向量检索器未初始化，返回空结果")
                return []
            
            # 使用预初始化的向量库进行检索
            results = await self.vector_retriever.execute_retrieval_with_super_prompt(query)
            
            # 限制返回数量
            limited_results = results[:max_examples] if results else []
            
            logger.info(f"向量检索完成: 返回 {len(limited_results)} 个示例")
            return limited_results
            
        except Exception as e:
            logger.error(f"向量检索失败: {e}")
            return []
    
    def _format_examples_for_context(self, examples: List[Dict[str, Any]]) -> str:
        """将检索到的示例格式化为上下文"""
        if not examples:
            return "No examples found."
        
        formatted_examples = []
        for i, example in enumerate(examples, 1):
            text = example.get('text', '')
            labels = example.get('label', {})
            
            entities_str = ", ".join(
                f"'{entity}' ({etype})" 
                for etype, entities in labels.items() 
                for entity in entities
            )
            
            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")
        
        return "\n\n".join(formatted_examples)
    
    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """🚀 单一调用完成NER任务 - 简化版本"""
        try:
            logger.info(f"🚀 开始单一Prompt NER: '{text[:50]}...'")

            # 🎯 简化策略：直接使用现有的ExtractionTool进行NER
            # 如果需要示例，可以预先检索并包含在prompt中

            # 检查是否需要示例（基于文本复杂度）
            if self._is_complex_text(text) and self.vector_retriever and self.vector_retriever.initialized:
                logger.info("检测到复杂文本，预先检索示例...")
                examples = await self._handle_vector_search(text, 3)
                examples_context = self._format_examples_for_context(examples)

                # 生成包含示例的增强Prompt
                enhanced_prompt = self._generate_enhanced_prompt(text, examples_context)
            else:
                # 生成简单的直接Prompt
                enhanced_prompt = self._generate_direct_prompt(text)

            # 准备Function Calling工具
            from schemas import ExtractionTool
            tools = [ExtractionTool]

            # 单次调用LLM
            messages = [{"role": "user", "content": enhanced_prompt}]
            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            # 解析结果
            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                tool_call = response.tool_calls[0]
                if tool_call.function and tool_call.function.arguments:
                    try:
                        import json
                        arguments = json.loads(tool_call.function.arguments)
                        entities = arguments.get("entities", {})

                        logger.info(f"✅ 单一Prompt NER完成: 识别 {sum(len(v) for v in entities.values())} 个实体")
                        return entities

                    except json.JSONDecodeError as e:
                        logger.error(f"解析工具参数失败: {e}")
                        return {}

            logger.warning("LLM未调用工具或返回无效结果")
            return {}

        except Exception as e:
            logger.error(f"单一Prompt NER失败: {e}")
            return {}

    def _is_complex_text(self, text: str) -> bool:
        """判断文本是否复杂，需要示例辅助"""
        # 简单的复杂度判断逻辑
        word_count = len(text.split())
        has_abbreviations = any(word.isupper() and len(word) > 1 for word in text.split())
        has_numbers = any(char.isdigit() for char in text)

        return word_count > 15 or has_abbreviations or has_numbers

    def _generate_enhanced_prompt(self, text: str, examples_context: str) -> str:
        """生成包含示例的增强Prompt"""
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        return f"""You are an expert Named Entity Recognition system.

Learn from these examples:
{examples_context}

Now extract named entities from this text using these entity types: {entity_types_str}

Text to analyze: "{text}"

Extract all named entities and return them using the extract_entities tool."""

    def _generate_direct_prompt(self, text: str) -> str:
        """生成直接的NER Prompt"""
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        return f"""You are an expert Named Entity Recognition system.

Extract named entities from the following text using these entity types: {entity_types_str}

Text to analyze: "{text}"

Extract all named entities and return them using the extract_entities tool."""


# 全局实例
single_prompt_ner = None

def get_single_prompt_ner(vector_retriever=None):
    """获取单一Prompt NER实例"""
    global single_prompt_ner
    if single_prompt_ner is None:
        single_prompt_ner = SinglePromptNER(vector_retriever)
    return single_prompt_ner
