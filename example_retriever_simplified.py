#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 简化的示例检索器 - 只保留基础向量存储和检索功能
遵循KISS原则：移除所有复杂的检索逻辑，由SinglePromptNER统一处理
"""

import asyncio
import logging
import pickle
import numpy as np
from typing import Dict, List, Any, Optional
from pathlib import Path

from config import CONFIG, get_current_dataset_info
from model_interface import model_service

logger = logging.getLogger(__name__)


class VectorStore:
    """简化的向量存储"""
    def __init__(self):
        self.examples: List[Dict[str, Any]] = []
        self.embeddings: List[List[float]] = []
        self.metadata: List[Dict[str, Any]] = []
        self._embeddings_matrix = None

    def add_example(self, example: Dict[str, Any], embedding: List[float], metadata: Dict[str, Any]):
        """添加示例到向量库"""
        self.examples.append(example)
        self.embeddings.append(embedding)
        self.metadata.append(metadata)
        self._embeddings_matrix = None  # 重置缓存

    def get_embeddings_matrix(self) -> np.ndarray:
        """获取嵌入矩阵（缓存优化）"""
        if self._embeddings_matrix is None and self.embeddings:
            self._embeddings_matrix = np.array(self.embeddings)
        return self._embeddings_matrix

    def similarity_search(self, query_embedding: List[float], top_k: int = 10) -> List[Dict[str, Any]]:
        """🚀 基础向量相似度搜索"""
        if not self.embeddings:
            return []

        query_vec = np.array(query_embedding)
        embeddings_matrix = self.get_embeddings_matrix()

        # 计算余弦相似度
        similarities = np.dot(embeddings_matrix, query_vec) / (
            np.linalg.norm(embeddings_matrix, axis=1) * np.linalg.norm(query_vec)
        )

        # 获取top_k结果
        top_indices = np.argsort(similarities)[::-1][:top_k]

        results = []
        for idx in top_indices:
            results.append({
                **self.examples[idx],
                'similarity_score': similarities[idx],
                'embedding': self.embeddings[idx]  # 保留嵌入用于后续处理
            })

        return results


class ExampleRetrieverSimplified:
    """🚀 简化的示例检索器 - 只负责向量存储和基础检索"""
    
    def __init__(self):
        self.vector_store = VectorStore()
        self.model_service = model_service
        self.initialized = False
        logger.info("简化示例检索器初始化完成")

    async def initialize_vector_store(self) -> bool:
        """初始化向量库"""
        try:
            current_dataset = get_current_dataset_info()
            data_path = current_dataset['train_path']
            
            # 生成缓存文件路径
            dataset_name = current_dataset['name'].replace(' ', '_').lower()
            pkl_cache_file = f"cache/vector_cache_{dataset_name}.pkl"
            
            # 检查缓存
            if Path(pkl_cache_file).exists():
                logger.info("🔍 检测到现有向量库，正在加载...")
                return await self._load_from_cache(pkl_cache_file)
            
            # 生成新的向量库
            logger.info("🚀 向量库不存在，开始生成...")
            return await self._generate_and_cache_vectors(data_path, pkl_cache_file)
            
        except Exception as e:
            logger.error(f"向量库初始化失败: {e}")
            return False

    async def _load_from_cache(self, pkl_file: str) -> bool:
        """从缓存加载向量库"""
        try:
            with open(pkl_file, 'rb') as f:
                cache_data = pickle.load(f)
            
            self.vector_store.examples = cache_data['examples']
            self.vector_store.embeddings = cache_data['embeddings']
            self.vector_store.metadata = cache_data['metadata']
            
            logger.info(f"从pkl缓存加载 {len(self.vector_store.examples)} 个示例")
            self.initialized = True
            logger.info("✅ 向量库加载成功")
            return True
            
        except Exception as e:
            logger.error(f"缓存加载失败: {e}")
            return False

    async def _generate_and_cache_vectors(self, data_path: str, pkl_file: str) -> bool:
        """生成并缓存向量"""
        try:
            import json
            
            # 加载训练数据
            with open(data_path, 'r', encoding='utf-8') as f:
                train_data = json.load(f)
            
            logger.info(f"开始为 {len(train_data)} 个示例生成向量...")
            
            # 批量生成嵌入
            texts = [item['text'] for item in train_data]
            embeddings = await self.model_service.get_embeddings_async(texts)
            
            if not embeddings or len(embeddings) != len(train_data):
                logger.error("嵌入生成失败")
                return False
            
            # 构建向量库
            for i, (item, embedding) in enumerate(zip(train_data, embeddings)):
                metadata = {
                    'entity_types': list(item.get('label', {}).keys()),
                    'text_length': len(item['text']),
                    'index': i
                }
                self.vector_store.add_example(item, embedding, metadata)
            
            # 保存缓存
            Path(pkl_file).parent.mkdir(parents=True, exist_ok=True)
            cache_data = {
                'examples': self.vector_store.examples,
                'embeddings': self.vector_store.embeddings,
                'metadata': self.vector_store.metadata
            }
            
            with open(pkl_file, 'wb') as f:
                pickle.dump(cache_data, f)
            
            logger.info(f"✅ 向量库生成完成，已缓存到 {pkl_file}")
            self.initialized = True
            return True
            
        except Exception as e:
            logger.error(f"向量生成失败: {e}")
            return False

    async def similarity_search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """🎯 基础相似度搜索 - 供SinglePromptNER调用"""
        try:
            if not self.initialized:
                logger.warning("向量库未初始化")
                return []
            
            # 生成查询嵌入
            query_embeddings = await self.model_service.get_embeddings_async([query])
            if not query_embeddings:
                logger.warning("查询嵌入生成失败")
                return []
            
            # 执行相似度搜索
            results = self.vector_store.similarity_search(query_embeddings[0], top_k)
            
            logger.info(f"相似度搜索完成: 返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"相似度搜索失败: {e}")
            return []

    async def rerank_examples(self, query: str, candidates: List[Dict[str, Any]], top_k: int = 5) -> List[Dict[str, Any]]:
        """🎯 重排器优化 - 供SinglePromptNER调用"""
        try:
            if not candidates:
                return []
            
            if len(candidates) <= top_k:
                return candidates
            
            # 准备重排器输入
            doc_texts = []
            for candidate in candidates:
                doc_text = f"Text: {candidate.get('text', '')}\nEntities: {candidate.get('label', {})}"
                doc_texts.append(doc_text)
            
            # 调用重排器
            rerank_result = await self.model_service.rerank_async(
                query=query,
                documents=doc_texts,
                top_k=top_k
            )
            
            if not rerank_result:
                return candidates[:top_k]
            
            # 重新排序
            reranked_candidates = []
            for i, rerank_item in enumerate(rerank_result):
                index = rerank_item.get('index', i)
                if index < len(candidates):
                    candidate = candidates[index].copy()
                    candidate['rerank_score'] = rerank_item.get('score', 0.5)
                    reranked_candidates.append(candidate)
            
            logger.info(f"🎯 Reranker processed {len(candidates)} docs, returned top-{len(reranked_candidates)}")
            return reranked_candidates
            
        except Exception as e:
            logger.error(f"重排器优化失败: {e}")
            return candidates[:top_k]


# 全局单例
example_retriever_simplified = ExampleRetrieverSimplified()
