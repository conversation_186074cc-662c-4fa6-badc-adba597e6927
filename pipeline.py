#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 Pipeline模块 - 重构后的简化版本
核心理念：使用单一Prompt NER系统替代复杂的多阶段流程
遵循KISS原则：简单、高效、优雅
"""

import logging
from typing import Dict, List

# 🚀 新架构：使用单一Prompt NER系统
from single_prompt_ner import get_single_prompt_ner

logger = logging.getLogger(__name__)

# --- 🚀 单一Prompt NER流程 (重构后的简化版本) ---

async def run_meta_cognitive_ner_pipeline(query: str, pre_initialized_retriever=None) -> Dict[str, List[str]]:
    """
    🚀 真正的单一Prompt NER流水线

    核心创新：
    1. LLM一次性完成思考、决策和执行
    2. 使用Function Calling让LLM自主决策是否需要示例
    3. 移除多阶段的复杂流程
    4. 实现真正的"单一超级Prompt"架构
    """
    try:
        logger.info(f"🚀 启动单一Prompt NER: '{query[:50]}...'")

        # 🎯 核心：使用单一Prompt NER系统
        ner_system = get_single_prompt_ner(pre_initialized_retriever)

        # 🚀 一次调用完成所有任务
        entities = await ner_system.extract_entities(query)

        # 显示结果
        if entities:
            total_entities = sum(len(v) for v in entities.values())
            logger.info(f"✅ 单一Prompt NER完成: 识别 {total_entities} 个实体")

            print("\n" + "="*60)
            print("🎯 单一Prompt NER结果")
            print("="*60)
            print(f"✅ 成功识别 {total_entities} 个实体")
            for entity_type, entity_list in entities.items():
                if entity_list:  # 只显示非空的实体类型
                    print(f"🏷️ {entity_type}: {entity_list}")
            print("="*60)
        else:
            logger.warning("⚠️ 未识别到任何实体")

        return entities

    except Exception as e:
        logger.error(f"❌ 单一Prompt NER失败: {e}")
        return {}


# 🎯 重构完成 - 真正的单一Prompt架构
# 使用方式：
# from pipeline import run_meta_cognitive_ner_pipeline
# result = await run_meta_cognitive_ner_pipeline("your query here", pre_initialized_retriever)
