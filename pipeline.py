import json
import logging
import re
from typing import Dict, List

from config import CONFIG
from schemas import ExtractionTool
from model_interface import model_service
# 注释：workflow_manager和example_retriever已被超级Prompt替代

# 优雅的单次显示控制
class DisplayOnce:
    def __init__(self):
        self.shown = set()

    def show(self, key: str, content_func):
        if key not in self.shown:
            content_func()
            self.shown.add(key)

display_once = DisplayOnce()

# --- 核心模块 ---

def clean_json_string(json_str: str) -> str:
    """清理JSON字符串，移除尾随逗号等常见错误"""
    if not json_str:
        return json_str
    
    # 移除尾随逗号
    json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
    
    # 移除对象末尾的逗号
    json_str = re.sub(r',(\s*})', r'\1', json_str)
    
    # 移除数组末尾的逗号
    json_str = re.sub(r',(\s*\])', r'\1', json_str)
    
    return json_str.strip()


def get_intent(query: str) -> str:
    """
    通过关键词快速识别用户查询的意图。
    """
    strategies = CONFIG.get('intent_strategies', {})
    for intent, config in strategies.items():
        if 'keywords' in config:
            for keyword in config['keywords']:
                if keyword in query:
                    return intent
    return 'default'

def load_samples_from_file(filepath: str) -> List[Dict]:
    """从JSON文件加载样本。"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logging.warning(f"Sample file not found: {filepath}, skipping.")
        return []
    except json.JSONDecodeError:
        logging.error(f"Could not decode JSON from {filepath}.")
        return []

class SampleSelector:
    """
    根据意图和策略，选择并组合ICL样本。
    """
    def __init__(self):
        self.axiomatic_pools = {}
        strategies = CONFIG.get('intent_strategies', {})
        for intent, config in strategies.items():
            pool_config = config.get('sample_pools', {}).get('axiomatic')
            if pool_config and 'file' in pool_config:
                self.axiomatic_pools[intent] = load_samples_from_file(pool_config['file'])
        
        self.semantic_pool = load_samples_from_file("data/CoNLL2003/train.json")

    def select_samples(self, query: str, intent: str) -> List[Dict]:
        """选择样本的核心逻辑。"""
        strategy = CONFIG['intent_strategies'].get(intent, CONFIG['intent_strategies']['default'])
        pools_config = strategy['sample_pools']
        
        selected_samples = []

        if 'axiomatic' in pools_config and intent in self.axiomatic_pools:
            pool = self.axiomatic_pools[intent]
            count = pools_config['axiomatic'].get('count', 0)
            if pool and count > 0:
                selected_samples.extend(pool[:count])

        if 'semantic' in pools_config:
            count = pools_config['semantic'].get('count', 0)
            if self.semantic_pool and count > 0:
                selected_samples.extend(self.semantic_pool[-count:])
        
        return selected_samples

def format_examples_for_prompt(samples: List[Dict]) -> str:
    """将样本格式化为清晰的字符串，用于Prompt。"""
    if not samples:
        return "No examples provided."
    
    formatted = []
    for sample in samples:
        text = sample.get('text', '')
        labels = sample.get('label', {})
        if not isinstance(labels, dict):
             continue

        entities_str = ", ".join(
            f"'{value}' ({etype})" 
            for etype, values in labels.items() 
            for value in values
        )
        formatted.append(f"Input: {text}\nOutput: [{entities_str}]")
    
    return "\n---\n".join(formatted)

# --- 🚀 智能体NER流程 (您的创新方案) ---

async def run_meta_cognitive_ner_pipeline(query: str, pre_initialized_retriever=None) -> Dict[str, List[str]]:
    """
    🚀 超级Prompt NER流水线 - 一次性完成NER任务

    核心优化：
    1. 跳过复杂的多步骤检索流程
    2. 使用超级Prompt直接进行NER
    3. 大幅提升性能和准确率
    """
    global _stage2_prompt_displayed, _stage2_output_displayed

    try:
        logging.info(f"🚀 Starting super prompt NER for: '{query[:50]}...'")

        from super_prompt_engine import super_prompt_engine
        from example_retriever import ExampleRetriever

        # 🚀 最强架构：元认知分析 + 检索驱动
        logging.info("🧠 Starting meta-cognitive analysis...")
        meta_result = await super_prompt_engine.execute_meta_cognitive_analysis(query)

        if meta_result.get('success'):
            logging.info(f"✅ Meta-cognitive analysis successful: {meta_result.get('description', 'N/A')}")

            # 🔍 KISS原则：使用预初始化的向量库，避免重复初始化
            if pre_initialized_retriever and pre_initialized_retriever.initialized:
                retriever = pre_initialized_retriever
                examples = await retriever.execute_retrieval_with_super_prompt(query)
                logging.info("✅ 使用预初始化向量库")
            else:
                # 备用方案：自动检测并初始化向量库
                retriever = ExampleRetriever()
                vector_ready = await retriever.initialize_vector_store()
                if vector_ready:
                    examples = await retriever.execute_retrieval_with_super_prompt(query)
                else:
                    logging.warning("⚠️ Vector store initialization failed, using direct mode")
                    examples = []

            # 生成检索驱动的提示
            prompt = super_prompt_engine.generate_retrieval_prompt(query, examples)
            logging.info(f"🔍 Using retrieval-driven prompt with {len(examples)} examples")

            # 🎯 优雅的单次显示
            display_once.show("stage2_prompt", lambda: (
                print("\n" + "="*80),
                print("🚀 第二阶段：NER执行提示词（检索模式）"),
                print("="*80),
                print(prompt),
                print("="*80)
            ))
        else:
            # 如果元认知分析失败，使用直接模式
            assessment = super_prompt_engine.assess_complexity(query)
            prompt = super_prompt_engine.generate_direct_prompt(query, assessment)
            logging.info(f"⚠️ Meta-cognitive failed, using direct mode: {assessment.level}")

            # 🎯 优雅的单次显示
            display_once.show("stage2_prompt", lambda: (
                print("\n" + "="*80),
                print("🚀 第二阶段：NER执行提示词（直接模式）"),
                print("="*80),
                print(prompt),
                print("="*80)
            ))

        # Step 2: 直接调用LLM进行NER
        messages = [{"role": "user", "content": prompt}]

        # 调用LLM进行NER推理
        response = await model_service.generate_with_tools_async(
            messages=messages,
            tools=[ExtractionTool]
        )

        if response and response.tool_calls:
            tool_call = response.tool_calls[0]
            if tool_call.function and tool_call.function.arguments:
                try:
                    # 清理JSON字符串
                    cleaned_args = clean_json_string(tool_call.function.arguments)
                    tool_instance = ExtractionTool.model_validate_json(cleaned_args)
                    entities = tool_instance.entities

                    # 🎯 优雅的单次显示
                    def show_stage2_output():
                        print("\n" + "="*80)
                        print("📊 第二阶段：NER执行输出")
                        print("="*80)
                        total_entities = sum(len(v) for v in entities.values())
                        print(f"✅ 成功识别 {total_entities} 个实体")
                        for entity_type, entity_list in entities.items():
                            if entity_list:  # 只显示非空的实体类型
                                print(f"🏷️ {entity_type}: {entity_list}")
                        print("="*80)

                    display_once.show("stage2_output", show_stage2_output)

                    # 记录成功的超级Prompt决策
                    total_entities = sum(len(v) for v in entities.values())
                    logging.info(f"✅ Super Prompt NER completed: {total_entities} entities found")

                    return entities
                except Exception as pydantic_error:
                    logging.error(f"❌ Pydantic validation failed: {pydantic_error}")
                    return {}

        logging.warning("⚠️ No valid tool calls in response, skipping")
        return {}

    except Exception as e:
        logging.warning(f"⚠️ Super Prompt NER pipeline failed: {e}")
        return {}


# Fallback机制已移除 - 遵循KISS原则和用户要求


# 优化的NER提示模板函数已移除 - 遵循KISS原则


# 🎯 pipeline.py - 简化的超级Prompt NER模块
# 遵循KISS原则：移除传统流程和fallback机制
# 使用方式：
# from pipeline import run_meta_cognitive_ner_pipeline
# result = await run_meta_cognitive_ner_pipeline("your query here")
